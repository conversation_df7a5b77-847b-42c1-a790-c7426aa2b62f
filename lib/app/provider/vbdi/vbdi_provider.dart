import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_chuyenldk_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_chitiet_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_dscv_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_dsldduyet_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_vb_lienquan_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_xuly_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/vbdi_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/dio_exception.dart' as dioError;
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class VbdiProvider {
  final dio = ApiRoot().dio;
  final GetStorage _store = GetStorage();
  // lấy danh sách duyệt văn bản đi
  Future<DsLdDuyetVbdi> auVbdiLdChoDuyet(int page, int size,
      [String? keyWord]) async {
    try {
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);

      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/2000";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "ma_ctcb_duyet": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "nam": 0,
        "ngay_duyet_den_ngay": denNgay,
        "ngay_duyet_tu_ngay": tuNgay,
        "trich_yeu": keyWord,
        "page": page,
        "size": size
      });
      final response =
          await dio.post(VbdiApi.auVbdiChoLanhDaoDuyet, data: data);
      return DsLdDuyetVbdi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách đã phát hành của văn thu
  Future<DsLdDuyetVbdi> auVbdiDaPhatHanhVt(int page, int size,
      [String? keyWord]) async {
    try {
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);

      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/2000";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "ma_ctcb_duyet": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "nam": 0,
        "ngay_duyet_den_ngay": denNgay,
        "ngay_duyet_tu_ngay": tuNgay,
        "trich_yeu": keyWord,
        "page": page,
        "size": size
      });
      final response =
          await dio.post(VbdiApi.auVbdiDaPhatHanhCuaVt, data: data);
      return DsLdDuyetVbdi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<DsLdDuyetVbdi> auVbdiUyQuyenLd(int page, int size,
      [String? keyWord]) async {
    try {
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);

      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/2000";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "ma_ctb_duyet": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "nam": 0,
        "ngay_duyet_den_ngay": denNgay,
        "ngay_duyet_tu_ngay": tuNgay,
        "trich_yeu": keyWord,
        "page": page,
        "size": size
      });
      final response = await dio.post(VbdiApi.auVbdiVbDuocUyQuyen, data: data);
      return DsLdDuyetVbdi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<DsLdDuyetVbdi> auVbdiDaChuyenVanThu(int page, int size,
      [String? keyWord]) async {
    try {
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);

      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/2000";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "ma_ctcb_duyet": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "nam": 0,
        "ngay_duyet_den_ngay": denNgay,
        "ngay_duyet_tu_ngay": tuNgay,
        "trich_yeu": keyWord,
        "page": page,
        "size": size
      });
      final response =
          await dio.post(VbdiApi.auVbdiChoPhatHanhCuaVt, data: data);
      return DsLdDuyetVbdi.fromJson(response.data);
    } on DioError catch (err) {
      final messageError = dioError.DioException.fromDioError(err).toString();
      return Future.error(messageError);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<VbdiChiTiet> auVbdiChiTiet(int maVanBanDi) async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      final reponse = await dio.get(
          "${VbdiApi.auVbdiChiTiet}?ma_van_ban_di=$maVanBanDi&ma_ctcb=$maCtcbKc");
      return VbdiChiTiet.fromJson(reponse.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<DsQtxlVbdi> auVbdiQtxl(int maVanBanDi) async {
    try {
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      final response = await dio.get(
          "${VbdiApi.auVbdiQtxl}?ma_van_ban_di=$maVanBanDi&ma_don_vi_quan_tri=$maDonViQuanTri");
      return DsQtxlVbdi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMesage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMesage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<CbDsVanThu> auVbdiDsVttDvQt() async {
    try {
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      final response = await dio
          .get("${VbdiApi.auVbdiDsVttDvQt}?ma_don_vi_quan_tri=$maDonViQuanTri");
      return CbDsVanThu.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    }
  }

  Future<CbDsLanhDaoKhacVbdi> auVbdiDsLdCDKhac() async {
    try {
      int maDonVi = _store.read(GetStorageKey.maDonVi);
      int maCtcb = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({"ma_ctcb": maCtcb, "ma_don_vi": maDonVi});
      final response = await dio.post(VbdiApi.auVbdidsLdCdKhac, data: data);
      return CbDsLanhDaoKhacVbdi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<CbDsChuyenVienVbdi> auVbdiDsCbCv() async {
    try {
      int maDonVi = _store.read(GetStorageKey.maDonViQuanTri);
      final response =
          await dio.get("${VbdiApi.auVbdiDsCbTdvTs}?ma_don_vi=$maDonVi");
      return CbDsChuyenVienVbdi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ResponseCommon> auVbdiLDCVT(
      int maVanBanDi,
      int maCtcbGui,
      String chuoiMaCtCbNhan,
      String noiDungChuyen,
      String fileDinhKem,
      String fileMoi,
      String? chuoiFileVanBanLienQuan,
      String? chuoiMaVanBanLienQuan,
      String chuoimaCtcbNhan,
      int loaiXuLy,
      int maXuLyDiCha,
      int sms) async {
    try {
      var data = FormData.fromMap({
        "chuoi_file_cu": fileDinhKem,
        "chuoi_file_vb_lien_quan_temp": chuoiFileVanBanLienQuan,
        "chuoi_ma_vb_lien_quan_temp": chuoiMaVanBanLienQuan,
        "ma_van_ban_di": maVanBanDi,
        "ma_ctcb_gui": maCtcbGui,
        "chuoi_ma_ctcb_nhan": chuoimaCtcbNhan,
        "ds_van_thu_nhan": chuoiMaCtCbNhan,
        "noi_dung_chuyen": noiDungChuyen,
        "file_dinh_kem": fileDinhKem,
        "file_moi": fileMoi,
        "loai_xu_ly": loaiXuLy,
        "ma_xu_ly_di_cha": maXuLyDiCha,
        "sms": sms
      });
      final response = await dio.post(VbdiApi.auVbdiLdCvt, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ResponseCommon> auVbdiLDDVBD(int maCtcbDuyet, int maVanBanDi) async {
    try {
      var data = FormData.fromMap(
          {"ma_ctcb_duyet": maCtcbDuyet, "ma_van_ban_di": maVanBanDi});
      final response = await dio.get(VbdiApi.auVbdiLDDvbd, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách vbd chưa xử lý của chuyen viên
  Future<DsCvXuLyVbdi> auVbdiDsChoXuLyCvVbdi(int page, int size,
      [String? trichYeu]) async {
    try {
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      int maCanBo = _store.read(GetStorageKey.maCanBo);

      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/2000";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "co_tep_tin": -1,
        "ma_can_bo": maCanBo,
        "ma_ctcb_cv": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_loai_ttdh": 0,
        "nam": 0,
        "ngay_tao_den_ngay": denNgay,
        "ngay_tao_tu_ngay": tuNgay,
        "page": page,
        "size": size,
        "trich_yeu": trichYeu
      });
      final response = await dio.post(VbdiApi.auVbdiDsVbdCxlCv, data: data);
      return DsCvXuLyVbdi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách đã xử lý vbdi của chuyên viên
  Future<DsCvXuLyVbdi> auVbdiDsDaXuLyCvVbdi(int page, int size,
      [String? trichYeu]) async {
    try {
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      int maCanBo = _store.read(GetStorageKey.maCanBo);

      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/2000";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "co_tep_tin": -1,
        "ma_can_bo": _store.read(GetStorageKey.maCanBo),
        "ma_ctcb_cv": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_loai_ttdh": 0,
        "nam": 0,
        "ngay_tao_den_ngay": denNgay,
        "ngay_tao_tu_ngay": tuNgay,
        "page": page,
        "size": size,
        "trang_thai_ttdh_gui": -1,
        "trich_yeu": trichYeu
      });
      final repsonse = await dio.post(VbdiApi.auVbdiDsVbDxlCv, data: data);
      return DsCvXuLyVbdi.fromJson(repsonse.data);
    } on DioError catch (err) {
      final errorMessave = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessave);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
  // danh sách chờ phát hành vbdi của chuyên vien

  Future<DsCvXuLyVbdi> auVbdiDsChoPhatHanhCv(int page, int size,
      [String? trichYeu]) async {
    try {
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      int maCanBo = _store.read(GetStorageKey.maCanBo);

      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/2000";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "co_tep_tin": -1,
        "ma_can_bo": _store.read(GetStorageKey.maCanBo),
        "ma_ctcb_cv": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_loai_ttdh": 0,
        "nam": 0,
        "ngay_tao_den_ngay": denNgay,
        "ngay_tao_tu_ngay": tuNgay,
        "page": page,
        "size": size,
        "trang_thai_ttdh_gui": -1,
        "trang_thai_xu_ly": 3,
        "trich_yeu": trichYeu
      });
      final response =
          await dio.post(VbdiApi.auVbdiDsVbdChoPhatHanh, data: data);
      return DsCvXuLyVbdi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
  // văn bản đi đã phát hành

  Future<DsCvXuLyVbdi> auVbdiDsDaPhatHanh(int page, int size,
      [String? trichYeu]) async {
    try {
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      int maCanBo = _store.read(GetStorageKey.maCanBo);

      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/2000";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }

      var data = FormData.fromMap({
        "ma_ctcb_cv": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "nam": 0,
        "ngay_di_den_ngay": denNgay,
        "ngay_di_tu_ngay": tuNgay,
        "page": page,
        "size": size,
        "trich_yeu": trichYeu
      });
      final response =
          await dio.post(VbdiApi.auVbdiDsVbdDaPhatHanh, data: data);
      return DsCvXuLyVbdi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // chuyển lãnh đạo khác
  Future<ResponseCommon> auVbdiLdcVbdCld(
      int maVanBanDi,
      int maCtcbGui,
      int maCtcbNhan,
      String? noiDungChuyen,
      String? fileDinhKem,
      int maXuLyDiCha,
      int sms,
      String? fileMoi) async {
    try {
      var data = FormData.fromMap({
        "ma_van_ban_di": maVanBanDi,
        "ma_ctcb_gui": maCtcbGui,
        "ma_ctcb_nhan": maCtcbNhan,
        "noi_dung_chuyen": noiDungChuyen,
        "file_dinh_kem": fileDinhKem,
        "ma_xu_ly_di_cha": maXuLyDiCha,
        "sms": sms,
        "file_moi": fileMoi
      });
      final response = await dio.post(VbdiApi.auVbdiLdVbdCld, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // chuyển chuyên viên
  Future<ResponseCommon> auVbdiLdCVbdCcv(
      int maVanBanDi,
      int maCtcbGui,
      int maCtcbNhan,
      String? noiDungChuyen,
      String? fileDinhKem,
      int maXuLyDiCha,
      int sms,
      String? fileMoi) async {
    try {
      var data = FormData.fromMap({
        "ma_van_ban_di": maVanBanDi,
        "ma_ctcb_gui": maCtcbGui,
        "ma_ctcb_nhan": maCtcbNhan,
        "noi_dung_chuyen": noiDungChuyen,
        "file_dinh_kem": fileDinhKem,
        "file_moi": fileMoi,
        "ma_xu_ly_di_cha": maXuLyDiCha,
        "sms": sms
      });
      final response = await dio.post(VbdiApi.auVbdiLdcVBDcCV, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Cập nhật đã xem
  Future<ResponseCommon> auVbdiCnDx(int maCtcb, int maVanBanDi) async {
    try {
      var data =
          FormData.fromMap({"ma_ctcb": maCtcb, "ma_van_ban_di": maVanBanDi});
      final response = await dio.get(VbdiApi.auVbdiCnDx, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (error) {
      final errorMessage = dioError.DioException.fromDioError(error).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Hoàn thành văn bản đi
  Future<ResponseCommon> auVbdiHoanTatVBDI(int maXuLyDi) async {
    try {
      var data = FormData.fromMap({"ma_xu_ly_di": maXuLyDi});
      final response = await dio.post(VbdiApi.auVbdiHtVbdi, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (error) {
      final errorMessage = dioError.DioException.fromDioError(error).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ResponseCommon> auVbdiCvChuyenVt(
      int maVanBanDi,
      int maCtcbGui,
      String chuoiMaCtcbNhan,
      String noiDungChuyen,
      String fileMoi,
      String fileDinhKem,
      int maXuLyDiCha,
      int sms,
      int maCtcbDuyet) async {
    try {
      var data = FormData.fromMap({
        "ma_van_ban_di": maVanBanDi,
        "ma_ctcb_gui": maCtcbGui,
        "chuoi_ma_ctcb_nhan": chuoiMaCtcbNhan,
        "noi_dung_chuyen": noiDungChuyen,
        "file_moi": fileMoi,
        "file_dinh_kem": fileDinhKem,
        "loai_xu_ly": 3,
        "ma_xu_ly_di_cha": maXuLyDiCha,
        "sms": sms,
        "ma_ctcb_duyet": maCtcbDuyet
      });
      final response = await dio.post(VbdiApi.auvbdiCvCvt, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (error) {
      final errorMessage = dioError.DioException.fromDioError(error).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<VbdiVBLienQuanDinhKem> auVbdiVbLienQuan(int maVanBan) async {
    try {
      final response =
          await dio.get('${VbdiApi.auVbdiVbLienQuan}?ma_van_ban=$maVanBan');
      return VbdiVBLienQuanDinhKem.fromJson(response.data);
    } on DioError catch (error) {
      final errorMessage = dioError.DioException.fromDioError(error).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ResponseCommon> auLuuFilePhieuTrinh(
      int maVanBanDi, String path) async {
    try {
      var data = FormData.fromMap({"ma_van_ban_di": maVanBanDi, "file": path});
      final response = await dio.post(VbdiApi.auVbdiPhieuTrinh, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (error) {
      final errorMessage = dioError.DioException.fromDioError(error).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
}
