import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vnpt_ioffice_camau/app/model/user/thong_tin_ca_nhan.dart';
import 'package:vnpt_ioffice_camau/app/provider/user/user_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class UserController extends GetxController
    with StateMixin<DetailThongTinCaNhan> {
  var userProvider = UserProvider();
  var isDangKyOtp = false.obs;
  var thongTinCaNhan = DetailThongTinCaNhan().obs;
  var _store = GetStorage();
  final _keyForm = GlobalKey<FormState>();

  TextEditingController textPassWordController = TextEditingController();
  TextEditingController textNewPassWordController = TextEditingController();
  TextEditingController textConfirmPassWordController = TextEditingController();
  void getThongTinCaNhan() async {
    try {
      change(null, status: RxStatus.loading());
      userProvider.getThongTinCaNhan().then((value) {
        change(value.data, status: RxStatus.success());
        thongTinCaNhan.value = value.data!;
      });
    } catch (exception) {
      change(null, status: RxStatus.error());
    }
  }

  Future<bool> checkSmartCaTichHop() async {
    final result = await userProvider.getThongTinCaNhan();
    if (result.data!.smartcaThUid != null &&
        result.data!.smartcaThPassword != null) {
      return true;
    } else {
      return false;
    }
  }

  Future<bool> onDangKyOtp() async {
    await userProvider
        .dangkyOTP(thongTinCaNhan.value.diDongCanBo!)
        .then((value) {
      print(value);
      if (value!.id == 1) {
        return true;
      }
    });
    return !isDangKyOtp.value;
  }

  String? validatePassword(String? passWord) {
    if (passWord == null || passWord.isEmpty) {
      return 'Vui lòng nhập mật khẩu mới';
    }
    if (passWord.length < 6) {
      return 'Mật khẩu có ít nhất 6 ký tự';
    }
    if (!RegExp(r'[0-9]').hasMatch(passWord)) {
      return 'Mật khẩu có ít nhất 1 con số';
    }
    if (!RegExp(r'[!@#\$&*~]').hasMatch(passWord)) {
      return 'Mật khẩu có ít nhất 1 ký tự đặt biệt';
    }
    return null;
  }

  void changePassWord() {
    if (_keyForm.currentState!.validate()) {
      checkMatKhau(textPassWordController.text);
      checkTrungMatKhau(textNewPassWordController.text);
    }
  }

  void checkMatKhau(String oldpassWord) async {
    await userProvider.kiemTraTrungMatKhau(oldpassWord).then((value) {
      Map<String, dynamic> user = value;
      if (user['trung_ma_mat_khau'] != 1) {
        CustomSnackBar.showWarningSnackBar(
            context: Get.context,
            title: "Thông báo!",
            message: "mật khẩu cũ không đúng!");
      }
    });
  }

  void checkTrungMatKhau(String passWord) async {
    await userProvider.kiemTraTrungMatKhau(passWord).then((value) {
      Map<String, dynamic> user = value;
      if (user['trung_ma_mat_khau'] == 1) {
        CustomSnackBar.showWarningSnackBar(
            context: Get.context,
            title: "Thông báo!",
            message: "Trùng mật khẩu cũ");
      } else {
        userProvider
            .capNhatMatKhau(
                textPassWordController.text,
                textNewPassWordController.text,
                textConfirmPassWordController.text)
            .then((value) {
          Map<String, dynamic> userUpdate = value;
          if (userUpdate['id'] == 1) {
            cleanInput();
            Get.back();
            CustomSnackBar.showSuccessSnackBar(
                context: Get.context,
                title: "Thông báo!",
                message: "Cập nhật thành công!");
          }
        });
      }
    });
  }

  var isShowPass = true.obs;
  var isShowNewPass = true.obs;
  var isShowConfirmPass = true.obs;
  void showModalDoiMatKhau() {
    Get.defaultDialog(
        title: "Đổi mật khẩu",
        content: Obx(
          () => SingleChildScrollView(
            child: Form(
              key: _keyForm,
              child: Column(
                children: [
                  Card(
                    color: Colors.red,
                    child: RichText(
                      text: const TextSpan(
                        text: 'Chú ý:\n',
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 15,
                            fontWeight: FontWeight.bold),
                        children: <InlineSpan>[
                          WidgetSpan(
                            child: Icon(Icons.arrow_right_outlined,
                                size: 24, color: Colors.yellow),
                          ),
                          TextSpan(
                              text:
                                  "Mật khẩu phải có ít nhất 6 ký tự, ít nhất 1 chữ viết hoa (A,B,C...)\n",
                              style:
                                  TextStyle(color: Colors.white, fontSize: 13)),
                          WidgetSpan(
                            child: Icon(Icons.arrow_right_outlined,
                                size: 24, color: Colors.yellow),
                          ),
                          TextSpan(
                              text:
                                  "Mật khẩu phải có ít nhất 1 con số (VD: 1,2,3,4,...).\n",
                              style:
                                  TextStyle(color: Colors.white, fontSize: 13)),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(5),
                    child: TextFormField(
                      validator: (value) {
                        if (value!.isEmpty) {
                          return "Vui lòng nhập mật khẩu cũ";
                        }
                      },
                      obscureText: isShowPass.value,
                      controller: textPassWordController,
                      decoration: InputDecoration(
                        labelText: 'Mật khẩu cũ',
                        border: const OutlineInputBorder(),
                        suffixIcon: IconButton(
                          icon: const Icon(
                            Icons.remove_red_eye,
                            color: Colors.blueAccent,
                          ),
                          onPressed: () {
                            isShowPass.value = !isShowPass.value;
                          },
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(5),
                    child: TextFormField(
                      obscureText: isShowNewPass.value,
                      controller: textNewPassWordController,
                      validator: (value) => validatePassword(value),
                      decoration: InputDecoration(
                        labelText: 'Mật khẩu mới',
                        border: const OutlineInputBorder(),
                        suffixIcon: IconButton(
                          icon: const Icon(
                            Icons.remove_red_eye,
                            color: Colors.blueAccent,
                          ),
                          onPressed: () {
                            isShowNewPass.value = !isShowNewPass.value;
                          },
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: TextFormField(
                      obscureText: isShowConfirmPass.value,
                      controller: textConfirmPassWordController,
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Vui lòng nhập mật khẩu';
                        }
                        if (value != textNewPassWordController.text) {
                          return 'Không khớp với mật khẩu mới';
                        }
                        return null;
                      },
                      decoration: InputDecoration(
                        labelText: 'Nhập lại mật khẩu mới',
                        border: const OutlineInputBorder(),
                        suffixIcon: IconButton(
                          icon: const Icon(
                            Icons.remove_red_eye,
                            color: Colors.blueAccent,
                          ),
                          onPressed: () {
                            isShowConfirmPass.value = !isShowConfirmPass.value;
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        onConfirm: () {
          changePassWord();
        },
        textConfirm: "Cập nhật",
        onCancel: () {
          Get.toNamed(Routers.TTCANHAN);
        },
        buttonColor: AppColor.blueAccentColor,
        cancelTextColor: AppColor.blueAccentColor,
        textCancel: "Đóng");
  }

  void dangXuat() {
    Get.defaultDialog(
        title: "Xác nhận",
        content: const Text("Bạn có muốn đăng xuất tài khoản không?"),
        onConfirm: () async {
          final SharedPreferences _prefs =
              await SharedPreferences.getInstance();
          await _prefs.clear();
          _store.erase();
          Get.offAllNamed(Routers.LOGIN);
        },
        textConfirm: "Đăng xuất",
        onCancel: () {
          Get.toNamed(Routers.TTCANHAN);
        },
        buttonColor: AppColor.blueAccentColor,
        cancelTextColor: AppColor.blueAccentColor,
        textCancel: "Đóng");
  }

  void cleanInput() {
    textConfirmPassWordController.clear();
    textPassWordController.clear();
    textNewPassWordController.clear();
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
  }

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    getThongTinCaNhan();
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    textConfirmPassWordController.dispose();
    textPassWordController.dispose();
    textNewPassWordController.dispose();
    super.dispose();
  }
}
