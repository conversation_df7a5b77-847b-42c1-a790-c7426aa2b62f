import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/global_widget/view_file_online.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/tracuu/tracuu_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/tracuu/tracuu_detail_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class TraCuuDetailVbdi extends GetView<TraCuuDetailController> {
  final SetupController setupController = Get.find();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: SingleChildScrollView(
      child: Obx(
        () => (controller.isLoadData.value == false)
            ? Padding(
                padding: const EdgeInsets.only(top: 5.0),
                child: WillPopScope(
                    child: Center(
                      child: SpinKitFadingCircle(
                          color: Get.isDarkMode
                              ? AppColor.yellowColor
                              : AppColor.blueAccentColor,
                          size: 40),
                    ),
                    onWillPop: () => Future.value(false)),
              )
            : Column(
                children: [
                  Container(
                    child: Column(
                      children: [
                        Card(
                            child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: "Trích yếu: ",
                                  style: DefaultTextStyle.of(context)
                                      .style, // Sử dụng phong cách mặc định
                                  children: <TextSpan>[
                                    TextSpan(
                                      text: controller
                                          .dataDetail.value.data?.trichYeu
                                          .toString(),
                                      style: const TextStyle(
                                          fontWeight: FontWeight
                                              .bold), // Phong cách in đậm
                                    )
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'Số ký hiệu: ',
                                  style: DefaultTextStyle.of(context)
                                      .style, // Sử dụng phong cách mặc định
                                  children: <TextSpan>[
                                    TextSpan(
                                      text: controller
                                          .dataDetail.value.data?.soKyHieu,
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color:
                                              Colors.red), // Phong cách in đậm
                                    )
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'Loại văn bản: ',
                                  style: DefaultTextStyle.of(context)
                                      .style, // Sử dụng phong cách mặc định
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: controller.dataDetail.value.data
                                            ?.tenLoaiVanBan

                                        // Phong cách in đậm
                                        )
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'Số bản phát hành: ',
                                  style: DefaultTextStyle.of(context).style,
                                  children: <TextSpan>[
                                    TextSpan(
                                      text: controller
                                          .dataDetail.value.data?.soBanPhatHanh,
                                    )
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'CQBH: ',
                                  style: DefaultTextStyle.of(context).style,
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: controller.dataDetail.value.data
                                            ?.tenCoQuanBanHanh,
                                        style: const TextStyle(
                                            color: AppColor.helpBlue))
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'Ngày lưu: ',
                                  style: DefaultTextStyle.of(context).style,
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: controller.dataDetail.value.data
                                                    ?.ngayLuu ==
                                                null
                                            ? ""
                                            : DateFormat('dd/MM/yyyy').format(
                                                controller.dataDetail.value
                                                    .data!.ngayLuu!))
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'Định danh: ',
                                  style: DefaultTextStyle.of(context).style,
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: controller.dataDetail.value.data
                                            ?.maDinhDanhVb)
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'Vai trò: ',
                                  style: DefaultTextStyle.of(context).style,
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: (controller.dataDetail.value.data
                                                    ?.maYeuCau!
                                                    .toInt() ==
                                                1
                                            ? ("Phối hợp xử lý")
                                            : (controller.dataDetail.value.data
                                                        ?.maYeuCau!
                                                        .toInt() ==
                                                    2)
                                                ? ("Xử lý chính")
                                                : ("Xem để biết")),
                                        style: const TextStyle(
                                            color: AppColor.helpBlue))
                                  ],
                                ),
                              ),
                            ),
                            Container(
                              child: ListView(
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  children: List.generate(
                                      (controller.dataDetail.value.data
                                                  ?.fileVanBan! ==
                                              null)
                                          ? 0
                                          : MethodUntils.getFileChiTietTTDH(
                                                  controller.dataDetail.value
                                                      .data!.fileVanBan!
                                                      .split(":"))
                                              .length, (index) {
                                    return ListTile(
                                      title: GestureDetector(
                                        onTap: () {
                                          ModalViewFileOnline.ViewFileOnline(
                                              tenFile: MethodUntils.getFileName(
                                                  controller.dataDetail.value!
                                                      .data!.fileVanBan!
                                                      .split(":")[index]
                                                      .toString()),
                                              path:
                                                  MethodUntils.getFileChiTietTTDH(
                                                          controller
                                                              .dataDetail
                                                              .value!
                                                              .data!
                                                              .fileVanBan!
                                                              .split(
                                                                  ":"))[index]
                                                      .urlViewFile!,
                                              item: controller.dataDetail.value!
                                                  .data!.fileVanBan!
                                                  .split(":")[index]);
                                          // setupController.openFile(
                                          //     url: MethodUntils
                                          //             .getFileChiTietTTDH(
                                          //                 controller
                                          //                     .dataDetail
                                          //                     .value!
                                          //                     .data!
                                          //                     .fileVanBan!
                                          //                     .split(
                                          //                         ":"))[index]
                                          //         .urlViewFile!,
                                          //     fileName:
                                          //         MethodUntils.getFileName(
                                          //             controller
                                          //                 .dataDetail
                                          //                 .value!
                                          //                 .data!
                                          //                 .fileVanBan!
                                          //                 .split(":")[index]
                                          //                 .toString()));
                                        },
                                        child: Row(
                                          children: [
                                            Icon(
                                              MethodUntils.getFileChiTietTTDH(
                                                      controller
                                                          .dataDetail
                                                          .value!
                                                          .data!
                                                          .fileVanBan!
                                                          .split(":"))[index]
                                                  .iconFile,
                                              color: MethodUntils
                                                      .getFileChiTietTTDH(
                                                          controller
                                                              .dataDetail
                                                              .value!
                                                              .data!
                                                              .fileVanBan!
                                                              .split(
                                                                  ":"))[index]
                                                  .colorIcon,
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 8.0),
                                              child: Text(
                                                  MethodUntils.getFileName(
                                                      controller
                                                          .dataDetail
                                                          .value!
                                                          .data!
                                                          .fileVanBan!
                                                          .split(":")[index]
                                                          .toString()),
                                                  style: const TextStyle(
                                                      color:
                                                          AppColor.blackColor,
                                                      fontSize: 13)),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  })),
                            ),
                            Container(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.only(left: 15),
                                    child: Text("Danh sách văn bản liên quan"),
                                  ),
                                  ListView(
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      shrinkWrap: true,
                                      children: List.generate(
                                          (controller.dsVanBanLienQuan.value ==
                                                  null)
                                              ? 0
                                              : MethodUntils.getFileChiTietTTDH(
                                                      controller
                                                          .dsVanBanLienQuan
                                                          .value!
                                                          .split(":"))
                                                  .length, (index) {
                                        return ListTile(
                                          title: GestureDetector(
                                            onTap: () {
                                              ModalViewFileOnline.ViewFileOnline(
                                                  tenFile:
                                                      MethodUntils.getFileName(
                                                          controller
                                                              .dsVanBanLienQuan
                                                              .value!
                                                              .split(":")[index]
                                                              .toString()),
                                                  path: MethodUntils.getFileChiTietTTDH(
                                                          controller
                                                              .dsVanBanLienQuan
                                                              .value!
                                                              .split(
                                                                  ":"))[index]
                                                      .urlViewFile!,
                                                  item: controller
                                                      .dsVanBanLienQuan.value!
                                                      .split(":")[index]);
                                            },
                                            child: Row(
                                              children: [
                                                Icon(
                                                  MethodUntils.getFileChiTietTTDH(
                                                          controller
                                                              .dsVanBanLienQuan
                                                              .value!
                                                              .split(
                                                                  ":"))[index]
                                                      .iconFile,
                                                  color: MethodUntils
                                                          .getFileChiTietTTDH(
                                                              controller
                                                                  .dsVanBanLienQuan
                                                                  .value!
                                                                  .split(
                                                                      ":"))[index]
                                                      .colorIcon,
                                                ),
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 8.0),
                                                  child: Text(
                                                      MethodUntils.getFileName(
                                                          controller
                                                              .dsVanBanLienQuan
                                                              .value!
                                                              .split(":")[index]
                                                              .toString()),
                                                      style: const TextStyle(
                                                          color: AppColor
                                                              .blackColor,
                                                          fontSize: 13)),
                                                ),
                                              ],
                                            ),
                                          ),
                                        );
                                      })),
                                ],
                              ),
                            ),
                            const Padding(
                              padding: EdgeInsets.only(bottom: 10),
                            ),
                            const Divider(
                              height: 2,
                              color: AppColor.greyColor,
                            ),
                          ],
                        )),
                        Visibility(
                          visible: controller.dsQtxlVbdi.isNotEmpty,
                          child: Padding(
                            padding: const EdgeInsets.all(10),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                const Text("Tổng hợp ý kiến xử lý",
                                    style: TextStyle(
                                        color: AppColor.blackColor,
                                        fontWeight: FontWeight.w600)),
                                const Padding(
                                    padding: EdgeInsets.only(bottom: 10)),
                                const Divider(
                                  height: 2,
                                  color: AppColor.greyColor,
                                ),
                                Obx(
                                  () => Column(
                                    children: controller.dsQtxlVbdi
                                        .map((qtxl) => Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  const SizedBox(
                                                    height: 5,
                                                  ),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.all(5),
                                                    child: Text(
                                                      "Người gửi: ${qtxl.canBoGui}",
                                                      style: const TextStyle(
                                                          color: AppColor
                                                              .helpBlue),
                                                    ),
                                                  ),
                                                  Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              5),
                                                      child: Text(
                                                        "Người nhận: ${qtxl.canBoNhan}",
                                                      )),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.all(5),
                                                    child: Text(
                                                        "Ngày nhận: ${qtxl.ngayNhan}"),
                                                  ),
                                                  const Divider(
                                                    color: Colors.grey,
                                                  )
                                                ]))
                                        .toList(),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    ));
  }
}
